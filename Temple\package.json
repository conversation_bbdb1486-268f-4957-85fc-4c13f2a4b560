{"name": "temple-donation-manager", "version": "1.0.0", "description": "A simple temple donation manager with a Node.js backend and MongoDB.", "main": "server.js", "type": "module", "scripts": {"start": "node server.js"}, "keywords": ["node", "express", "mongodb", "mongoose", "donation"], "author": "Gemini", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.4.4", "pdf-lib": "^1.17.1", "python-shell": "^5.0.0"}}